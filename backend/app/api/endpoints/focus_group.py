"""
API endpoints for Focus Group Simulator.
"""
import logging
import re
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import JSONResponse

from app.schemas.focus_group import (
    FocusGroupRequest, FocusGroupResponse,
    TextAssistRequest, TextAssistResponse
)
from app.services.focus_group_service import focus_group_service
from app.core.auth import get_current_user_from_token
from app.core.supabase import SupabaseService

logger = logging.getLogger(__name__)
router = APIRouter()


def get_supabase_service() -> SupabaseService:
    """Dependency to get Supabase service instance."""
    return SupabaseService()


@router.post("/simulate-focus-group", response_model=FocusGroupResponse)
async def simulate_focus_group(
    request: FocusGroupRequest,
    req: Request,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> FocusGroupResponse:
    """
    Simulate a focus group session with diverse participants.

    This endpoint creates a realistic focus group simulation with:
    - Diverse participant profiles (demographics, personalities, behaviors)
    - Natural conversation flow based on participant characteristics
    - Sentiment analysis of responses
    - Comprehensive insights and recommendations
    - Demographic pattern analysis

    Args:
        request: Focus group simulation parameters
        req: FastAPI request object

    Returns:
        Complete focus group simulation results with discussions and summary
    """
    try:
        request_id = req.headers.get("X-Request-ID", "unknown")
        logger.info(f"Focus group simulation request: {request_id}")

        # Validate request
        if not request.content or len(request.content.strip()) < 10:
            raise HTTPException(
                status_code=400,
                detail="El contenido debe tener al menos 10 caracteres"
            )

        # Validate content quality
        if not _is_valid_content(request.content):
            raise HTTPException(
                status_code=400,
                detail="Por favor describe una idea real de producto o servicio. El contenido actual parece ser texto sin sentido."
            )

        # Run simulation
        result = await focus_group_service.simulate_focus_group(request)

        # Save to database if user is authenticated (not anonymous)
        if current_user["user_id"] != "anonymous" and result.status == "success":
            try:
                simulation_data = {
                    "content": request.content,
                    "product_category": request.product_category,
                    "context": request.context,
                    "custom_questions": request.questions or [],
                    "num_participants": request.num_participants or 6,
                    "discussion_rounds": request.discussion_rounds or 3,
                    "simulation_results": result.dict(),
                    "participants": result.focus_group_simulation.discussions[0].conversation if result.focus_group_simulation and result.focus_group_simulation.discussions else [],
                    "discussions": [d.dict() for d in result.focus_group_simulation.discussions] if result.focus_group_simulation else [],
                    "summary": result.focus_group_simulation.summary.dict() if result.focus_group_simulation else {}
                }

                saved_simulation = await supabase_service.save_focus_group_simulation(
                    user_id=current_user["user_id"],
                    simulation_data=simulation_data
                )

                if saved_simulation:
                    logger.info(f"Focus group simulation saved to database for user {current_user['user_id']}")

                    # Clean up old simulations (keep only last 5 recent ones)
                    await supabase_service.cleanup_old_focus_group_simulations(
                        user_id=current_user["user_id"],
                        keep_recent=5
                    )
                else:
                    logger.warning("Failed to save focus group simulation to database")

            except Exception as e:
                logger.error(f"Error saving focus group simulation: {str(e)}")
                # Don't fail the request if database save fails

        logger.info(f"Focus group simulation completed: {request_id}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in focus group simulation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno en la simulación: {str(e)}"
        )


@router.post("/text-autocorrect", response_model=TextAssistResponse)
async def text_autocorrect(
    request: TextAssistRequest,
    req: Request
) -> TextAssistResponse:
    """
    Auto-correct and improve text content with AI assistance.
    This is an alias for text-assist to maintain compatibility.
    """
    return await text_assist(request, req)


@router.post("/text-assist", response_model=TextAssistResponse)
async def text_assist(
    request: TextAssistRequest,
    req: Request
) -> TextAssistResponse:
    """
    Improve text content with AI assistance.

    This endpoint provides intelligent text improvement suggestions:
    - Context-aware improvements
    - Multiple suggestion variants
    - Optimized for different content types
    - Enhanced clarity and impact

    Args:
        request: Text improvement parameters
        req: FastAPI request object

    Returns:
        Text improvement suggestions
    """
    try:
        request_id = req.headers.get("X-Request-ID", "unknown")
        logger.info(f"Text assist request: {request_id}")

        # Validate request
        if not request.content or len(request.content.strip()) < 3:
            raise HTTPException(
                status_code=400,
                detail="El contenido debe tener al menos 3 caracteres"
            )

        # Validate content quality
        if not _is_valid_content(request.content):
            raise HTTPException(
                status_code=400,
                detail="Por favor escribe texto real que quieras mejorar. El contenido actual parece ser texto sin sentido."
            )

        # Improve text
        result = await focus_group_service.improve_text(request)

        logger.info(f"Text assist completed: {request_id}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in text assist: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno en la mejora de texto: {str(e)}"
        )


@router.get("/focus-group/recent")
async def get_recent_focus_groups(
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service),
    limit: int = 5
) -> Dict[str, Any]:
    """
    Get recent focus group simulations for the current user.

    Args:
        current_user: Current authenticated user
        supabase_service: Supabase service instance
        limit: Maximum number of simulations to return

    Returns:
        List of recent focus group simulations
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to access saved focus groups"
        )

    try:
        simulations = await supabase_service.get_recent_focus_group_simulations(
            user_id=current_user["user_id"],
            limit=limit
        )

        return {
            "success": True,
            "simulations": simulations,
            "count": len(simulations)
        }

    except Exception as e:
        logger.error(f"Error getting recent focus groups: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error retrieving recent focus groups"
        )


@router.get("/focus-group/favorites")
async def get_favorite_focus_groups(
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service),
    limit: int = 50
) -> Dict[str, Any]:
    """
    Get favorite focus group simulations for the current user.

    Args:
        current_user: Current authenticated user
        supabase_service: Supabase service instance
        limit: Maximum number of simulations to return

    Returns:
        List of favorite focus group simulations
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to access saved focus groups"
        )

    try:
        simulations = await supabase_service.get_favorite_focus_group_simulations(
            user_id=current_user["user_id"],
            limit=limit
        )

        return {
            "success": True,
            "simulations": simulations,
            "count": len(simulations)
        }

    except Exception as e:
        logger.error(f"Error getting favorite focus groups: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error retrieving favorite focus groups"
        )


@router.get("/focus-group/{simulation_id}")
async def get_focus_group_by_id(
    simulation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Get a specific focus group simulation by ID.

    Args:
        simulation_id: The simulation ID
        current_user: Current authenticated user
        supabase_service: Supabase service instance

    Returns:
        The focus group simulation data
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to access saved focus groups"
        )

    try:
        simulation = await supabase_service.get_focus_group_simulation_by_id(
            simulation_id=simulation_id,
            user_id=current_user["user_id"]
        )

        if not simulation:
            raise HTTPException(
                status_code=404,
                detail="Focus group simulation not found or access denied"
            )

        # Update view count
        await supabase_service.update_focus_group_simulation(
            simulation_id=simulation_id,
            user_id=current_user["user_id"],
            update_data={
                "view_count": simulation.get("view_count", 0) + 1,
                "last_viewed_at": "now()"
            }
        )

        return {
            "success": True,
            "simulation": simulation
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting focus group by ID: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error retrieving focus group simulation"
        )


@router.post("/focus-group/{simulation_id}/toggle-favorite")
async def toggle_focus_group_favorite(
    simulation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Toggle the favorite status of a focus group simulation.

    Args:
        simulation_id: The simulation ID
        current_user: Current authenticated user
        supabase_service: Supabase service instance

    Returns:
        Updated simulation data
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to manage focus groups"
        )

    try:
        updated_simulation = await supabase_service.toggle_focus_group_favorite(
            simulation_id=simulation_id,
            user_id=current_user["user_id"]
        )

        if not updated_simulation:
            raise HTTPException(
                status_code=404,
                detail="Focus group simulation not found or access denied"
            )

        return {
            "success": True,
            "simulation": updated_simulation,
            "is_favorite": updated_simulation.get("is_favorite", False)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling focus group favorite: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error updating focus group favorite status"
        )


@router.post("/focus-group/{simulation_id}/rename")
async def rename_focus_group(
    simulation_id: str,
    request: Dict[str, str],
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Rename a focus group simulation.

    Args:
        simulation_id: The simulation ID
        request: Request body with new name
        current_user: Current authenticated user
        supabase_service: Supabase service instance

    Returns:
        Updated simulation data
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to manage focus groups"
        )

    new_name = request.get("name", "").strip()
    if not new_name:
        raise HTTPException(
            status_code=400,
            detail="Name is required"
        )

    try:
        updated_simulation = await supabase_service.update_focus_group_simulation(
            simulation_id=simulation_id,
            user_id=current_user["user_id"],
            update_data={"custom_name": new_name}
        )

        if not updated_simulation:
            raise HTTPException(
                status_code=404,
                detail="Focus group simulation not found or access denied"
            )

        return {
            "success": True,
            "simulation": updated_simulation,
            "message": "Focus group renamed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error renaming focus group: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error renaming focus group simulation"
        )


@router.delete("/focus-group/{simulation_id}")
async def delete_focus_group(
    simulation_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Delete a focus group simulation.

    Args:
        simulation_id: The simulation ID
        current_user: Current authenticated user
        supabase_service: Supabase service instance

    Returns:
        Success confirmation
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to manage focus groups"
        )

    try:
        success = await supabase_service.delete_focus_group_simulation(
            simulation_id=simulation_id,
            user_id=current_user["user_id"]
        )

        if not success:
            raise HTTPException(
                status_code=404,
                detail="Focus group simulation not found or access denied"
            )

        return {
            "success": True,
            "message": "Focus group simulation deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting focus group: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error deleting focus group simulation"
        )


@router.get("/focus-group/health")
async def focus_group_health() -> Dict[str, Any]:
    """
    Health check endpoint for focus group services.

    Returns:
        Service health status
    """
    return {
        "status": "healthy",
        "service": "focus_group_simulator",
        "version": "1.0.0",
        "endpoints": [
            "/simulate-focus-group",
            "/text-assist",
            "/focus-group/recent",
            "/focus-group/favorites",
            "/focus-group/{simulation_id}",
            "/focus-group/{simulation_id}/toggle-favorite",
            "/focus-group/{simulation_id}/rename",
            "/focus-group/{simulation_id}"
        ]
    }


def _is_valid_content(content: str) -> bool:
    """
    Validate that content is meaningful and not random text.

    Args:
        content: Content to validate

    Returns:
        True if content appears to be meaningful, False otherwise
    """
    content = content.strip().lower()

    # Check minimum length
    if len(content) < 5:
        return False

    # Check for excessive repetition of characters
    if re.search(r'(.)\1{4,}', content):  # Same character repeated 5+ times
        return False

    # Check for random keyboard mashing patterns
    random_patterns = [
        r'[qwertyuiop]{5,}',  # Keyboard row patterns
        r'[asdfghjkl]{5,}',
        r'[zxcvbnm]{5,}',
        r'[fvgdhbsjaknsjbchvbdjnkasdjsbhcjn]{10,}',  # Random sequences
        r'^[bcdfghjklmnpqrstvwxyz]{8,}$',  # Too many consonants without vowels
    ]

    for pattern in random_patterns:
        if re.search(pattern, content):
            return False

    # Check vowel ratio (meaningful text should have some vowels)
    vowels = len(re.findall(r'[aeiouáéíóú]', content))
    consonants = len(re.findall(r'[bcdfghjklmnpqrstvwxyzñ]', content))

    if consonants > 0 and vowels / (vowels + consonants) < 0.15:  # Less than 15% vowels
        return False

    # Check for meaningful words (at least some recognizable patterns)
    meaningful_patterns = [
        r'\b(app|aplicacion|producto|servicio|idea|negocio|empresa|startup)\b',
        r'\b(para|que|con|sin|como|donde|cuando|porque)\b',
        r'\b(gente|personas|usuarios|clientes|mercado)\b',
        r'\b(hacer|crear|desarrollar|vender|comprar|usar)\b',
        r'\b(mejor|bueno|malo|facil|dificil|rapido|lento)\b',
        r'\b(casa|trabajo|escuela|tienda|online|internet)\b'
    ]

    meaningful_matches = 0
    for pattern in meaningful_patterns:
        if re.search(pattern, content):
            meaningful_matches += 1

    # If it has at least one meaningful pattern, it's probably valid
    if meaningful_matches > 0:
        return True

    # Check for basic sentence structure (spaces between words)
    words = content.split()
    if len(words) >= 2:  # At least 2 words
        return True

    return False
